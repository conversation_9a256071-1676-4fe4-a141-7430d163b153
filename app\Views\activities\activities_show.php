<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-activity me-2"></i>Activity Details
                            </h2>
                            <p class="text-muted mb-0">View detailed information about this activity</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/activities') ?>" class="btn btn-secondary me-2">
                                <i class="bi bi-arrow-left me-2"></i>Back to Activities
                            </a>
                            <?php if ($activity['status'] === 'completed' || $activity['status'] === 'active'): ?>
                                <a href="<?= base_url('admin/activities/' . $activity['id'] . '/supervise') ?>" class="btn btn-primary">
                                    <i class="bi bi-clipboard-check me-2"></i>Supervise
                                </a>
                            <?php elseif ($activity['status'] !== 'approved'): ?>
                                <a href="<?= base_url('admin/activities/' . $activity['id'] . '/edit') ?>" class="btn btn-warning">
                                    <i class="bi bi-pencil me-2"></i>Edit Activity
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Activity Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Activity Name</label>
                            <p class="h5 text-dark"><?= esc($activity['activity_name']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Activity Type</label>
                            <p>
                                <?php
                                $typeClass = '';
                                $typeIcon = '';
                                switch ($activity['activity_type']) {
                                    case 'price_collection':
                                        $typeClass = 'bg-primary';
                                        $typeIcon = 'bi-currency-dollar';
                                        break;
                                    case 'market_survey':
                                        $typeClass = 'bg-info';
                                        $typeIcon = 'bi-graph-up';
                                        break;
                                    case 'business_registration':
                                        $typeClass = 'bg-success';
                                        $typeIcon = 'bi-building';
                                        break;
                                    case 'data_verification':
                                        $typeClass = 'bg-warning';
                                        $typeIcon = 'bi-check-square';
                                        break;
                                    case 'training':
                                        $typeClass = 'bg-secondary';
                                        $typeIcon = 'bi-book';
                                        break;
                                    default:
                                        $typeClass = 'bg-dark';
                                        $typeIcon = 'bi-gear';
                                }
                                ?>
                                <span class="badge <?= $typeClass ?> fs-6">
                                    <i class="<?= $typeIcon ?> me-1"></i><?= ucwords(str_replace('_', ' ', $activity['activity_type'])) ?>
                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Workplan</label>
                            <p class="text-dark">
                                <i class="bi bi-calendar-check me-1"></i>
                                <?= esc($activity['workplan_title']) ?>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Status</label>
                            <p>
                                <?php
                                $statusClass = '';
                                $statusIcon = '';
                                switch ($activity['status']) {
                                    case 'active':
                                        $statusClass = 'bg-success';
                                        $statusIcon = 'bi-check-circle';
                                        break;
                                    case 'submitted':
                                        $statusClass = 'bg-info';
                                        $statusIcon = 'bi-upload';
                                        break;
                                    case 'completed':
                                        $statusClass = 'bg-warning';
                                        $statusIcon = 'bi-check-circle';
                                        break;
                                    case 'approved':
                                        $statusClass = 'bg-primary';
                                        $statusIcon = 'bi-check-circle-fill';
                                        break;
                                    case 'redo':
                                        $statusClass = 'bg-warning';
                                        $statusIcon = 'bi-arrow-clockwise';
                                        break;
                                    case 'cancelled':
                                        $statusClass = 'bg-danger';
                                        $statusIcon = 'bi-x-circle';
                                        break;
                                    default:
                                        $statusClass = 'bg-secondary';
                                        $statusIcon = 'bi-question-circle';
                                }
                                ?>
                                <span class="badge <?= $statusClass ?> fs-6">
                                    <i class="<?= $statusIcon ?> me-1"></i><?= ucfirst($activity['status']) ?>
                                </span>


                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Start Date</label>
                            <p class="text-dark">
                                <i class="bi bi-calendar-event me-1"></i>
                                <?= date('F d, Y', strtotime($activity['date_from'])) ?>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">End Date</label>
                            <p class="text-dark">
                                <i class="bi bi-calendar-event me-1"></i>
                                <?= date('F d, Y', strtotime($activity['date_to'])) ?>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Duration</label>
                            <p class="text-dark">
                                <i class="bi bi-clock me-1"></i>
                                <?php
                                $start = new DateTime($activity['date_from']);
                                $end = new DateTime($activity['date_to']);
                                $interval = $start->diff($end);
                                echo $interval->days + 1; // +1 to include both start and end dates
                                ?> day<?= ($interval->days + 1) > 1 ? 's' : '' ?>
                            </p>
                        </div>
                    </div>

                    <?php if (!empty($activity['remarks'])): ?>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">Remarks</label>
                                <div class="border rounded p-3 bg-light">
                                    <?= nl2br(esc($activity['remarks'])) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($activity['status_remarks'])): ?>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">Status Remarks</label>
                                <div class="border rounded p-3 bg-light">
                                    <?= nl2br(esc($activity['status_remarks'])) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Status Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-square me-2"></i>Status Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($activity['status_by_name'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Updated By</label>
                            <p class="text-dark">
                                <i class="bi bi-person me-1"></i>
                                <?= esc($activity['status_by_name']) ?>
                            </p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($activity['status_at'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Updated At</label>
                            <p class="text-dark">
                                <i class="bi bi-clock me-1"></i>
                                <?= date('F d, Y \a\t g:i A', strtotime($activity['status_at'])) ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Creation Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Creation Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Created By</label>
                        <p class="text-dark">
                            <i class="bi bi-person me-1"></i>
                            <?= esc($activity['created_by_name']) ?>
                        </p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Created At</label>
                        <p class="text-dark">
                            <i class="bi bi-calendar-plus me-1"></i>
                            <?= date('F d, Y \a\t g:i A', strtotime($activity['created_at'])) ?>
                        </p>
                    </div>

                    <?php if (!empty($activity['updated_at']) && $activity['updated_at'] !== $activity['created_at']): ?>
                        <div class="mb-0">
                            <label class="form-label text-muted">Last Updated</label>
                            <p class="text-dark">
                                <i class="bi bi-pencil-square me-1"></i>
                                <?= date('F d, Y \a\t g:i A', strtotime($activity['updated_at'])) ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- User Assignment Section -->
    <div class="row mt-4" id="assigned-users">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people me-2"></i>Assigned Users
                    </h5>
                    <span class="badge bg-primary"><?= count($assigned_users) ?> Users</span>
                </div>
                <div class="card-body">
                    <!-- Add User Form -->
                    <?php if ($activity['status'] !== 'approved' && $activity['status'] !== 'completed'): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <form action="<?= base_url('admin/activities/' . $activity['id'] . '/assign-user') ?>" method="post" class="d-flex gap-2 align-items-end">
                                    <?= csrf_field() ?>
                                    <div class="flex-grow-1">
                                        <label for="user_id" class="form-label">Assign User</label>
                                        <select class="form-select" id="user_id" name="user_id" required>
                                            <option value="">Select a user...</option>
                                            <?php foreach ($available_users as $user): ?>
                                                <?php
                                                // Check if user is already assigned
                                                $isAssigned = false;
                                                foreach ($assigned_users as $assignedUser) {
                                                    if ($assignedUser['user_id'] == $user['id']) {
                                                        $isAssigned = true;
                                                        break;
                                                    }
                                                }
                                                ?>
                                                <?php if (!$isAssigned): ?>
                                                    <option value="<?= $user['id'] ?>">
                                                        <?= esc($user['name']) ?> (<?= esc($user['email']) ?>)
                                                    </option>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-1"></i>Assign
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Assigned Users List -->
                    <?php if (!empty($assigned_users)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Position</th>
                                        <th>Assigned Date</th>
                                        <?php if ($activity['status'] !== 'approved' && $activity['status'] !== 'completed'): ?>
                                            <th width="100">Actions</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($assigned_users as $assignedUser): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <?= strtoupper(substr($assignedUser['name'], 0, 1)) ?>
                                                    </div>
                                                    <div>
                                                        <div class="fw-medium"><?= esc($assignedUser['name']) ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?= esc($assignedUser['email']) ?></td>
                                            <td><?= esc($assignedUser['position'] ?? 'Not specified') ?></td>
                                            <td><?= date('M d, Y H:i', strtotime($assignedUser['assigned_at'])) ?></td>
                                            <?php if ($activity['status'] !== 'approved' && $activity['status'] !== 'completed'): ?>
                                                <td>
                                                    <form action="<?= base_url('admin/activities/' . $activity['id'] . '/remove-user/' . $assignedUser['user_id']) ?>" method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to remove this user from the activity?')">
                                                        <?= csrf_field() ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-3">No Users Assigned</h6>
                            <p class="text-muted">No users have been assigned to this activity yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Business Location Assignment Section -->
    <div class="row mt-4" id="assigned-businesses">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-geo-alt me-2"></i>Assigned Business Locations
                    </h5>
                    <span class="badge bg-info"><?= count($assigned_businesses) ?> Locations</span>
                </div>
                <div class="card-body">
                    <!-- Add Business Location Form -->
                    <?php if ($activity['status'] !== 'approved' && $activity['status'] !== 'completed'): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <form action="<?= base_url('admin/activities/' . $activity['id'] . '/assign-business') ?>" method="post" class="d-flex gap-2 align-items-end">
                                    <?= csrf_field() ?>
                                    <div class="flex-grow-1">
                                        <label for="business_location_id" class="form-label">Assign Business Location</label>
                                        <select class="form-select" id="business_location_id" name="business_location_id" required>
                                            <option value="">Select a business location...</option>
                                            <?php foreach ($available_businesses as $business): ?>
                                                <?php
                                                // Check if business is already assigned
                                                $isAssigned = false;
                                                foreach ($assigned_businesses as $assignedBusiness) {
                                                    if ($assignedBusiness['business_location_id'] == $business['id']) {
                                                        $isAssigned = true;
                                                        break;
                                                    }
                                                }
                                                ?>
                                                <?php if (!$isAssigned): ?>
                                                    <option value="<?= $business['id'] ?>">
                                                        <?= esc($business['business_name']) ?>
                                                        <?php if ($business['district_name']): ?>
                                                            - <?= esc($business['district_name']) ?>, <?= esc($business['province_name']) ?>
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-info">
                                            <i class="bi bi-plus-circle me-1"></i>Assign
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Assigned Business Locations List -->
                    <?php if (!empty($assigned_businesses)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Business Name</th>
                                        <th>Location</th>
                                        <th>GPS Coordinates</th>
                                        <th>Assigned Date</th>
                                        <?php if ($activity['status'] !== 'approved' && $activity['status'] !== 'completed'): ?>
                                            <th width="100">Actions</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($assigned_businesses as $assignedBusiness): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-shop"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-medium"><?= esc($assignedBusiness['business_name']) ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?= esc($assignedBusiness['district_name']) ?>, <?= esc($assignedBusiness['province_name']) ?>
                                                <br><small class="text-muted"><?= esc($assignedBusiness['country_name']) ?></small>
                                            </td>
                                            <td>
                                                <?php if ($assignedBusiness['gps_coordinates']): ?>
                                                    <small class="text-muted font-monospace"><?= esc($assignedBusiness['gps_coordinates']) ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">Not available</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('M d, Y H:i', strtotime($assignedBusiness['assigned_at'])) ?></td>
                                            <?php if ($activity['status'] !== 'approved' && $activity['status'] !== 'completed'): ?>
                                                <td>
                                                    <form action="<?= base_url('admin/activities/' . $activity['id'] . '/remove-business/' . $assignedBusiness['business_location_id']) ?>" method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to remove this business location from the activity?')">
                                                        <?= csrf_field() ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-geo-alt text-muted" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-3">No Business Locations Assigned</h6>
                            <p class="text-muted">No business locations have been assigned to this activity yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<?= $this->endSection() ?>
