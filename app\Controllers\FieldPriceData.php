<?php

namespace App\Controllers;

use App\Models\ActivityModel;
use App\Models\ActivityBusinessLocationModel;
use App\Models\ActivityPriceCollectionDataModel;
use App\Models\BusinessLocationModel;
use App\Models\PriceDataModel;

class FieldPriceData extends BaseController
{
    protected $activityModel;
    protected $activityLocationModel;
    protected $priceCollectionModel;
    protected $businessLocationModel;
    protected $priceDataModel;

    public function __construct()
    {
        $this->activityModel = new ActivityModel();
        $this->activityLocationModel = new ActivityBusinessLocationModel();
        $this->priceCollectionModel = new ActivityPriceCollectionDataModel();
        $this->businessLocationModel = new BusinessLocationModel();
        $this->priceDataModel = new PriceDataModel();
    }

    // GET: field/collect
    public function index()
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        $orgId = (int) session()->get('field_org_id');
        $userId = (int) session()->get('field_user_id');

        // Get activities assigned to the current user only
        $activityUserModel = new \App\Models\ActivityUserModel();
        $activities = $this->activityModel
            ->select('activities.*')
            ->join('activity_users', 'activity_users.activity_id = activities.id', 'inner')
            ->where('activities.org_id', $orgId)
            ->where('activity_users.user_id', $userId)
            ->where('activities.is_deleted', false)
            ->orderBy('activities.date_from', 'DESC')
            ->findAll();

        // Enrich with assignment counts (locations & users)
        foreach ($activities as &$act) {
            $actId = (int)($act['id'] ?? 0);
            $act['location_count'] = $this->activityLocationModel->getAssignmentStatsByActivity($actId);
            $act['user_count'] = $activityUserModel->where('activity_id', $actId)->countAllResults();
        }
        unset($act);

        $data = [
            'title' => 'Activities - Price Collection',
            'activities' => $activities,
        ];

        return view('field_price_data/field_price_data_list', $data);
    }

    // GET: field/collect/activity/{id}
    public function activity(int $activityId)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        $orgId = (int) session()->get('field_org_id');

        $activity = $this->activityModel
            ->where('id', $activityId)
            ->where('org_id', $orgId)
            ->where('is_deleted', false)
            ->first();
        if (!$activity) {
            return redirect()->to('field/collect')->with('error', 'Activity not found.');
        }

        $locations = $this->activityLocationModel->getLocationsByActivity($activityId);

        // Get entry counts for each location
        $priceDataModel = new \App\Models\ActivityPriceCollectionDataModel();

        // Add entry counts to locations
        foreach ($locations as &$location) {
            $businessLocationId = $location['business_location_id'];

            // Count entries excluding deleted records (using deleted_at field only)
            $count = $priceDataModel->where('activity_id', $activityId)
                                   ->where('business_location_id', $businessLocationId)
                                   ->where('deleted_at', null)
                                   ->countAllResults();

            $location['entry_count'] = $count;
        }

        $data = [
            'title' => 'Business Locations - ' . $activity['activity_name'],
            'activity' => $activity,
            'locations' => $locations,
        ];

        return view('field_price_data/field_price_data_activity', $data);
    }



    // GET: field/collect/activity/{activityId}/location/{locationId}
    public function location(int $activityId, int $businessLocationId)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        $orgId = (int) session()->get('field_org_id');

        $activity = $this->activityModel
            ->where('id', $activityId)
            ->where('org_id', $orgId)
            ->where('is_deleted', false)
            ->first();
        if (!$activity) {
            return redirect()->to('field/collect')->with('error', 'Activity not found.');
        }

        $location = $this->businessLocationModel
            ->where('id', $businessLocationId)
            ->where('is_deleted', false)
            ->first();
        if (!$location) {
            return redirect()->to('field/collect/activity/' . $activityId)->with('error', 'Business location not found.');
        }

        // Verify assignment (optional safety)
        if (!$this->activityLocationModel->isLocationAssignedToActivity($activityId, $businessLocationId)) {
            return redirect()->to('field/collect/activity/' . $activityId)->with('error', 'Location is not assigned to this activity.');
        }

        // Existing price entries for this combination with group and brand information
        $entries = $this->priceCollectionModel
            ->select('activity_price_collection_data.*, goods_items.item as item_name, goods_brands.brand_name, goods_groups.group_name, users.name as created_by_name')
            ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
            ->join('goods_brands', 'goods_brands.id = goods_items.goods_brand_id', 'left')
            ->join('goods_groups', 'goods_groups.id = goods_items.goods_group_id', 'left')
            ->join('users', 'users.id = activity_price_collection_data.created_by', 'left')
            ->where('activity_price_collection_data.activity_id', $activityId)
            ->where('activity_price_collection_data.business_location_id', $businessLocationId)
            ->orderBy('goods_groups.group_name', 'ASC')
            ->orderBy('goods_brands.brand_name', 'ASC')
            ->orderBy('goods_items.item', 'ASC')
            ->findAll();
        // Map entries by item for quick lookup to enforce one entry per item per location in UI
        $entriesByItem = [];
        foreach ($entries as $en) {
            if (isset($en['item_id'])) {
                $entriesByItem[$en['item_id']] = $en;
            }
        }

        // Items list with brand type (primary/substitute) and group information for quick scrolling
        $db = \Config\Database::connect();
        $items = $db->table('goods_items gi')
            ->select('gi.id, gi.item, gb.type as brand_type, gb.brand_name, gg.group_name')
            ->join('goods_brands gb', 'gb.id = gi.goods_brand_id', 'left')
            ->join('goods_groups gg', 'gg.id = gi.goods_group_id', 'left')
            ->where('gi.is_deleted', false)
            ->where('gi.status', 'active')
            ->orderBy('gb.type', 'ASC')
            ->orderBy('gg.group_name', 'ASC')
            ->orderBy('gb.brand_name', 'ASC')
            ->orderBy('gi.item', 'ASC')
            ->get()->getResultArray();

        $data = [
            'title' => 'Collect Prices - ' . $location['business_name'],
            'activity' => $activity,
            'location' => $location,
            'entries' => $entries,
            'entriesByItem' => $entriesByItem,
            'items' => $items,
        ];

        return view('field_price_data/field_price_data_location', $data);
    }

    // POST: field/collect/create
    public function create()
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        if (!$this->request->is('post')) {
            return redirect()->to('field/collect');
        }

        $orgId = (int) session()->get('field_org_id');
        $userId = (int) session()->get('field_user_id');

        $activityId = (int) $this->request->getPost('activity_id');
        $businessLocationId = (int) $this->request->getPost('business_location_id');
        $itemId = (int) $this->request->getPost('item_id');
        $price = $this->request->getPost('price');
        $remarks = $this->request->getPost('remarks');

        $redirect = 'field/collect/activity/' . $activityId . '/location/' . $businessLocationId;

        $rules = [
            'activity_id' => 'required|is_natural_no_zero',
            'business_location_id' => 'required|is_natural_no_zero',
            'item_id' => 'required|is_natural_no_zero',
            'price' => 'required|decimal|greater_than[0]',
            'remarks' => 'permit_empty|max_length[65535]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->to($redirect)->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'org_id' => $orgId,
            'business_location_id' => $businessLocationId,
            'activity_id' => $activityId,
            'user_id' => $userId,
            'item_id' => $itemId,
            'price' => $price,
            'remarks' => $remarks,
            'status' => 'active',
            'created_by' => $userId,
        ];

        try {
            $this->priceCollectionModel->insert($data);
            return redirect()->to($redirect)->with('success', 'Price entry added successfully.');
        } catch (\Throwable $e) {
            return redirect()->to($redirect)->withInput()->with('error', $e->getMessage());
        }
    }

    // GET: field/collect/edit/{id}
    public function edit(int $id)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $entry = $this->priceCollectionModel
            ->select('activity_price_collection_data.*, goods_items.item as item_name, business_locations.business_name, activities.activity_name')
            ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
            ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
            ->join('activities', 'activities.id = activity_price_collection_data.activity_id', 'left')
            ->where('activity_price_collection_data.id', $id)
            ->first();

        if (!$entry) {
            return redirect()->to('field/collect')->with('error', 'Price entry not found.');
        }

        $data = [
            'title' => 'Edit Price Entry',
            'entry' => $entry,
        ];

        return view('field_price_data/field_price_data_edit', $data);
    }

    // POST: field/collect/update/{id}
    public function update(int $id)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        if (!$this->request->is('post')) {
            return redirect()->to('field/collect');
        }

        $entry = $this->priceCollectionModel->find($id);
        if (!$entry || ($entry['is_deleted'] ?? false)) {
            return redirect()->to('field/collect')->with('error', 'Price entry not found.');
        }

        $rules = [
            'price' => 'required|decimal|greater_than[0]',
            'remarks' => 'permit_empty|max_length[65535]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $userId = (int) session()->get('field_user_id');

        $data = [
            'price' => $this->request->getPost('price'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $userId,
        ];

        try {
            $this->priceCollectionModel->update($id, $data);
            $redirect = 'field/collect/activity/' . $entry['activity_id'] . '/location/' . $entry['business_location_id'];
            return redirect()->to($redirect)->with('success', 'Price entry updated successfully.');
        } catch (\Throwable $e) {
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }
    }


}

