<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-clipboard-check me-2"></i>Supervise Activity
                            </h2>
                            <p class="text-muted mb-0">Review collected data and manage activity status</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/activities') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Activities
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Activity Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label text-muted">Activity Name</label>
                            <p class="h6 text-dark"><?= esc($activity['activity_name']) ?></p>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label text-muted">Type</label>
                            <p>
                                <span class="badge bg-primary">
                                    <i class="bi bi-currency-dollar me-1"></i><?= ucwords(str_replace('_', ' ', $activity['activity_type'])) ?>
                                </span>
                            </p>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label text-muted">Workplan</label>
                            <p class="text-dark"><?= esc($activity['workplan_title']) ?></p>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label text-muted">Status</label>
                            <p>
                                <span class="badge bg-warning fs-6">
                                    <i class="bi bi-check-circle me-1"></i><?= ucfirst($activity['status']) ?>
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label text-muted">Date Range</label>
                            <p class="text-dark">
                                <i class="bi bi-calendar-event me-1"></i>
                                <?= date('M d, Y', strtotime($activity['date_from'])) ?> - <?= date('M d, Y', strtotime($activity['date_to'])) ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">Total Entries</label>
                            <p class="text-dark">
                                <i class="bi bi-list-ol me-1"></i>
                                <strong><?= count($priceData) ?></strong> price entries collected
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label text-muted">Assigned Users</label>
                            <p class="text-dark">
                                <i class="bi bi-people me-1"></i>
                                <strong><?= $assignedUsersCount ?></strong> user<?= $assignedUsersCount != 1 ? 's' : '' ?> assigned
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-muted">Business Locations</label>
                            <p class="text-dark">
                                <i class="bi bi-geo-alt me-1"></i>
                                <strong><?= $assignedBusinessesCount ?></strong> location<?= $assignedBusinessesCount != 1 ? 's' : '' ?> assigned
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Price Collection Data -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table me-2"></i>Collected Price Data
                    </h5>
                    <span class="badge bg-info"><?= count($priceData) ?> Entries</span>
                </div>
                <div class="card-body">
                    <?php if (empty($priceData)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Data Collected</h4>
                            <p class="text-muted">No price data has been collected for this activity yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Business Location</th>
                                        <th>Type</th>
                                        <th>Goods Group</th>
                                        <th>Brand</th>
                                        <th>Brand Type</th>
                                        <th>Items</th>
                                        <th>Price</th>
                                        <th>Created By</th>
                                        <th>Created At</th>
                                        <th>Updated At</th>
                                        <th>Updated By</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($priceData as $index => $data): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary"><?= $index + 1 ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-shop"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-medium"><?= esc($data['business_name']) ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge <?= $data['business_type'] === 'retail' ? 'bg-success' : 'bg-warning' ?>">
                                                    <?= ucfirst($data['business_type']) ?>
                                                </span>
                                            </td>
                                            <td><?= esc($data['goods_group_name']) ?></td>
                                            <td><?= esc($data['brand_name']) ?></td>
                                            <td>
                                                <span class="badge <?= $data['brand_type'] === 'primary' ? 'bg-primary' : 'bg-secondary' ?>">
                                                    <?= ucfirst($data['brand_type']) ?>
                                                </span>
                                            </td>
                                            <td><?= esc($data['item_name']) ?></td>
                                            <td>
                                                <strong class="text-success">K <?= number_format($data['price'], 2) ?></strong>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <?= strtoupper(substr($data['created_by_name'] ?? 'U', 0, 1)) ?>
                                                    </div>
                                                    <small><?= esc($data['created_by_name'] ?? 'Unknown') ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($data['created_at'])) ?><br>
                                                    <?= date('H:i', strtotime($data['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= $data['updated_at'] ? date('M d, Y', strtotime($data['updated_at'])) : '-' ?><br>
                                                    <?= $data['updated_at'] ? date('H:i', strtotime($data['updated_at'])) : '' ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if (!empty($data['updated_by_name'])): ?>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                            <?= strtoupper(substr($data['updated_by_name'], 0, 1)) ?>
                                                        </div>
                                                        <small><?= esc($data['updated_by_name']) ?></small>
                                                    </div>
                                                <?php else: ?>
                                                    <small class="text-muted">-</small>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Management -->
    <?php if ($activity['status'] === 'completed'): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear me-2"></i>Status Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Review the collected data above and choose an action:</strong><br>
                            <strong>Resend:</strong> Send the activity back to active status for additional data collection<br>
                            <strong>Approve:</strong> Approve the activity and mark it as completed
                        </div>

                        <div class="d-flex gap-3">
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#resendModal">
                                <i class="bi bi-arrow-clockwise me-1"></i>Resend for More Data
                            </button>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal">
                                <i class="bi bi-check-circle me-1"></i>Approve Activity
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-check-circle-fill me-2"></i>Activity Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>This activity has been approved.</strong><br>
                            The data collection is complete and the activity status is final. You can review the collected data above for reference.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php if ($activity['status'] === 'completed'): ?>
<!-- Resend Modal -->
<div class="modal fade" id="resendModal" tabindex="-1" aria-labelledby="resendModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resendModalLabel">
                    <i class="bi bi-arrow-clockwise me-2"></i>Resend Activity
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('admin/activities/' . $activity['id'] . '/resend') ?>" method="post">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="resend_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="resend_remarks" name="status_remarks" rows="3" placeholder="Enter remarks for resending the activity..."></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        This will change the activity status back to <strong>Active</strong> and allow field users to continue data collection.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-arrow-clockwise me-1"></i>Resend Activity
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">
                    <i class="bi bi-check-circle me-2"></i>Approve Activity
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('admin/activities/' . $activity['id'] . '/approve') ?>" method="post">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="approve_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="approve_remarks" name="status_remarks" rows="3" placeholder="Enter approval remarks..."></textarea>
                    </div>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i>
                        This will change the activity status to <strong>Approved</strong> and finalize the data collection.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i>Approve Activity
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<?= $this->endSection() ?>
