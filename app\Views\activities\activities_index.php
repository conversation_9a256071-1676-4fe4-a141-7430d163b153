<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-activity me-2"></i>Activities Management
                                <?php if (isset($selected_workplan)): ?>
                                    <small class="text-muted">- <?= esc($selected_workplan['title']) ?></small>
                                <?php endif; ?>
                            </h2>
                            <p class="text-muted mb-0">
                                <?php if (isset($selected_workplan)): ?>
                                    Activities for workplan: <?= esc($selected_workplan['title']) ?>
                                    <span class="text-primary">(<?= date('M d, Y', strtotime($selected_workplan['date_from'])) ?> - <?= date('M d, Y', strtotime($selected_workplan['date_to'])) ?>)</span>
                                <?php else: ?>
                                    Manage and monitor activities within your workplans
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <?php if (isset($selected_workplan)): ?>
                                <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-outline-secondary me-2">
                                    <i class="bi bi-house me-1"></i>Dashboard
                                </a>
                                <a href="<?= base_url('admin/workplans/' . $selected_workplan['id']) ?>" class="btn btn-secondary me-2">
                                    <i class="bi bi-calendar-check me-2"></i>View Workplan
                                </a>
                                <a href="<?= base_url('admin/activities/new?workplan_id=' . $selected_workplan['id']) ?>" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-2"></i>Add Activity
                                </a>
                            <?php else: ?>
                                <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary me-2">
                                    <i class="bi bi-house me-2"></i>Back to Dashboard
                                </a>
                                <a href="<?= base_url('admin/activities/new') ?>" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-2"></i>Create New Activity
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Activities Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>All Activities
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($activities)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-activity text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Activities Found</h4>
                            <p class="text-muted">Get started by creating your first activity.</p>
                            <a href="<?= base_url('admin/activities/new') ?>" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Create New Activity
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Activity Name</th>
                                        <th>Type</th>
                                        <th>Workplan</th>
                                        <th>Date Range</th>
                                        <th>Assigned Users</th>
                                        <th>Assigned Locations</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($activities as $index => $activity): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary"><?= $index + 1 ?></span>
                                            </td>
                                            <td>
                                                <strong class="text-dark"><?= esc($activity['activity_name']) ?></strong>
                                                <?php if (!empty($activity['remarks'])): ?>
                                                    <br><small class="text-muted"><?= esc(substr($activity['remarks'], 0, 50)) ?><?= strlen($activity['remarks']) > 50 ? '...' : '' ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $typeClass = '';
                                                $typeIcon = '';
                                                switch ($activity['activity_type']) {
                                                    case 'price_collection':
                                                        $typeClass = 'bg-primary';
                                                        $typeIcon = 'bi-currency-dollar';
                                                        break;
                                                    case 'market_survey':
                                                        $typeClass = 'bg-info';
                                                        $typeIcon = 'bi-graph-up';
                                                        break;
                                                    case 'business_registration':
                                                        $typeClass = 'bg-success';
                                                        $typeIcon = 'bi-building';
                                                        break;
                                                    case 'data_verification':
                                                        $typeClass = 'bg-warning';
                                                        $typeIcon = 'bi-check-square';
                                                        break;
                                                    case 'training':
                                                        $typeClass = 'bg-secondary';
                                                        $typeIcon = 'bi-book';
                                                        break;
                                                    default:
                                                        $typeClass = 'bg-dark';
                                                        $typeIcon = 'bi-gear';
                                                }
                                                ?>
                                                <span class="badge <?= $typeClass ?>">
                                                    <i class="<?= $typeIcon ?> me-1"></i><?= ucwords(str_replace('_', ' ', $activity['activity_type'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div>
                                                    <i class="bi bi-calendar-check me-1"></i>
                                                    <strong><?= esc($activity['workplan_title']) ?></strong>
                                                </div>
                                                <?php if (!empty($activity['supervisor_name'])): ?>
                                                    <small class="text-muted">
                                                        <i class="bi bi-person-badge me-1"></i>Supervisor: <?= esc($activity['supervisor_name']) ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">From:</small><br>
                                                <span class="text-dark"><?= date('M d, Y', strtotime($activity['date_from'])) ?></span><br>
                                                <small class="text-muted">To:</small><br>
                                                <span class="text-dark"><?= date('M d, Y', strtotime($activity['date_to'])) ?></span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info">
                                                    <i class="bi bi-people me-1"></i><?= $activity['assigned_users_count'] ?>
                                                </span>
                                                <?php if ($activity['assigned_users_count'] > 0): ?>
                                                    <br><small class="text-muted">users</small>
                                                <?php else: ?>
                                                    <br><small class="text-muted">no users</small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-warning">
                                                    <i class="bi bi-geo-alt me-1"></i><?= $activity['assigned_businesses_count'] ?>
                                                </span>
                                                <?php if ($activity['assigned_businesses_count'] > 0): ?>
                                                    <br><small class="text-muted">locations</small>
                                                <?php else: ?>
                                                    <br><small class="text-muted">no locations</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusIcon = '';
                                                switch ($activity['status']) {
                                                    case 'active':
                                                        $statusClass = 'bg-success';
                                                        $statusIcon = 'bi-check-circle';
                                                        break;
                                                    case 'submitted':
                                                        $statusClass = 'bg-info';
                                                        $statusIcon = 'bi-upload';
                                                        break;
                                                    case 'approved':
                                                        $statusClass = 'bg-primary';
                                                        $statusIcon = 'bi-check-circle-fill';
                                                        break;
                                                    case 'redo':
                                                        $statusClass = 'bg-warning';
                                                        $statusIcon = 'bi-arrow-clockwise';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusIcon = 'bi-x-circle';
                                                        break;
                                                    default:
                                                        $statusClass = 'bg-secondary';
                                                        $statusIcon = 'bi-question-circle';
                                                }
                                                ?>
                                                <span class="badge <?= $statusClass ?>">
                                                    <i class="<?= $statusIcon ?> me-1"></i><?= ucfirst($activity['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($activity['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('admin/activities/' . $activity['id']) ?>"
                                                       class="btn btn-sm btn-outline-info"
                                                       title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <?php if ($activity['status'] === 'completed' || $activity['status'] === 'approved'): ?>
                                                        <a href="<?= base_url('admin/activities/' . $activity['id'] . '/supervise') ?>"
                                                           class="btn btn-sm btn-outline-primary"
                                                           title="Supervise">
                                                            <i class="bi bi-clipboard-check"></i>
                                                        </a>
                                                    <?php elseif ($activity['status'] !== 'approved'): ?>
                                                        <a href="<?= base_url('admin/activities/' . $activity['id'] . '/edit') ?>"
                                                           class="btn btn-sm btn-outline-warning"
                                                           title="Edit">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-danger"
                                                                title="Delete"
                                                                onclick="confirmDelete(<?= $activity['id'] ?>, '<?= esc($activity['activity_name']) ?>')">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Are you sure you want to delete the activity:</p>
                <p><strong id="activityName"></strong></p>
                <p class="text-warning">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    This action cannot be undone.
                </p>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>Delete Activity
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(activityId, activityName) {
    document.getElementById('activityName').textContent = activityName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/activities/') ?>' + activityId + '/delete';
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>
